<script lang='ts' setup>
import { watch, ref, computed, onMounted } from 'vue'
import type { ICrmSendSmsParams, ILeadData } from '@/types/lead'
import crmService from '@/service/crmService'
import systemService from '@/service/systemService'
import type { ISMSTemplatePageResponseItem } from '@/types/sms' 
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    selectedData:ILeadData[]
}>()
const dialogVisible = ref(false)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})
const form = ref<ICrmSendSmsParams>({})
const emit = defineEmits(['closeVisible'])
const handleClose = (val?:string) => {
    form.value = {}
    emit('closeVisible',val)
}
const handleSubmit = () => {
    if (!form.value.smsModelId) {
        ElMessage.error('请选择模板')
        return
    }
    const params = {
        leadIds: selectedIds.value,
        smsModelId: form.value.smsModelId
    }
    crmService.crmSendSms(params).then((res) => {
        if(res.success){
            ElMessage.success('短信发送成功')
            handleClose()
        }else{
            ElMessage.error(res.errMsg)
        }
    })
}
const totalNum = computed(() => props.selectedData.length)
const selectedIds = computed(() => {
    if (props.selectedData) {
        return props.selectedData.map(item=>item.id) 
    } else {
        return []
    }
})
const modelList = ref<ISMSTemplatePageResponseItem[]>()
const modelContent = computed(() => {
    if(form.value.smsModelId){
        return modelList.value?.find(item=>item.id===form.value.smsModelId)?.content || ''
    }else{
        return ''
    }
})
onMounted(() => {
    systemService.smsTemplatePage({page:1,pageSize:999,enable:true}).then((res) => {
        modelList.value = res.data
    })
})
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        title="发送短信"
        width="500"
        show-close
        destory-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form :model="form" label-position="top">
            <div class="b-margin-16">收信人 <span class="l-margin-16">{{ totalNum }}</span></div>
            <el-form-item label="选择短信模板" >
                <el-select 
                    v-model="form.smsModelId"
                    placeholder="选择短信模板"
                    clearable
                >
                    <el-option 
                        v-for="item in modelList"
                        :key="item.id"
                        :label="item.templateName"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="短信内容预览">
                <el-input v-model="modelContent" type="textarea" disabled></el-input>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose('cancel')">取消</el-button>
                    <el-button type="primary" @click="handleSubmit()">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang='scss'>
</style>
